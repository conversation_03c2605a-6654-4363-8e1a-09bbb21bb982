name: 'Post Coverage Comment Action'
description: 'Prepares and posts a code coverage comment to a PR.'

inputs:
  cli_json_file:
    description: 'Path to CLI coverage-summary.json'
    required: true
  core_json_file:
    description: 'Path to Core coverage-summary.json'
    required: true
  cli_full_text_summary_file:
    description: 'Path to CLI full-text-summary.txt'
    required: true
  core_full_text_summary_file:
    description: 'Path to Core full-text-summary.txt'
    required: true
  node_version:
    description: 'Node.js version for context in messages'
    required: true
  github_token:
    description: 'GitHub token for posting comments'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Prepare Coverage Comment
      id: prep_coverage_comment
      shell: bash
      run: |
        cli_json_file="${{ inputs.cli_json_file }}"
        core_json_file="${{ inputs.core_json_file }}"
        cli_full_text_summary_file="${{ inputs.cli_full_text_summary_file }}"
        core_full_text_summary_file="${{ inputs.core_full_text_summary_file }}"
        comment_file="coverage-comment.md"

        # Extract percentages using jq for the main table
        if [ -f "$cli_json_file" ]; then
          cli_lines_pct=$(jq -r '.total.lines.pct' "$cli_json_file")
          cli_statements_pct=$(jq -r '.total.statements.pct' "$cli_json_file")
          cli_functions_pct=$(jq -r '.total.functions.pct' "$cli_json_file")
          cli_branches_pct=$(jq -r '.total.branches.pct' "$cli_json_file")
        else
          cli_lines_pct="N/A"; cli_statements_pct="N/A"; cli_functions_pct="N/A"; cli_branches_pct="N/A"
          echo "CLI coverage-summary.json not found at: $cli_json_file" >&2 # Error to stderr
        fi

        if [ -f "$core_json_file" ]; then
          core_lines_pct=$(jq -r '.total.lines.pct' "$core_json_file")
          core_statements_pct=$(jq -r '.total.statements.pct' "$core_json_file")
          core_functions_pct=$(jq -r '.total.functions.pct' "$core_json_file")
          core_branches_pct=$(jq -r '.total.branches.pct' "$core_json_file")
        else
          core_lines_pct="N/A"; core_statements_pct="N/A"; core_functions_pct="N/A"; core_branches_pct="N/A"
          echo "Core coverage-summary.json not found at: $core_json_file" >&2 # Error to stderr
        fi

        echo "## Code Coverage Summary" > "$comment_file"
        echo "" >> "$comment_file"
        echo "| Package | Lines | Statements | Functions | Branches |" >> "$comment_file"
        echo "|---|---|---|---|---|" >> "$comment_file"
        echo "| CLI | ${cli_lines_pct}% | ${cli_statements_pct}% | ${cli_functions_pct}% | ${cli_branches_pct}% |" >> "$comment_file"
        echo "| Core | ${core_lines_pct}% | ${core_statements_pct}% | ${core_functions_pct}% | ${core_branches_pct}% |" >> "$comment_file"
        echo "" >> "$comment_file"

        # CLI Package - Collapsible Section (with full text summary from file)
        echo "<details>" >> "$comment_file"
        echo "<summary>CLI Package - Full Text Report</summary>" >> "$comment_file"
        echo "" >> "$comment_file"
        echo '```text' >> "$comment_file"
        if [ -f "$cli_full_text_summary_file" ]; then
          cat "$cli_full_text_summary_file" >> "$comment_file"
        else
          echo "CLI full-text-summary.txt not found at: $cli_full_text_summary_file" >> "$comment_file"
        fi
        echo '```' >> "$comment_file"
        echo "</details>" >> "$comment_file"
        echo "" >> "$comment_file"

        # Core Package - Collapsible Section (with full text summary from file)
        echo "<details>" >> "$comment_file"
        echo "<summary>Core Package - Full Text Report</summary>" >> "$comment_file"
        echo "" >> "$comment_file"
        echo '```text' >> "$comment_file"
        if [ -f "$core_full_text_summary_file" ]; then
          cat "$core_full_text_summary_file" >> "$comment_file"
        else
          echo "Core full-text-summary.txt not found at: $core_full_text_summary_file" >> "$comment_file"
        fi
        echo '```' >> "$comment_file"
        echo "</details>" >> "$comment_file"
        echo "" >> "$comment_file"

        echo "_For detailed HTML reports, please see the 'coverage-reports-${{ inputs.node_version }}' artifact from the main CI run._" >> "$comment_file"

    - name: Post Coverage Comment
      uses: thollander/actions-comment-pull-request@v3
      if: always()
      with:
        file-path: coverage-comment.md # Use the generated file directly
        comment-tag: code-coverage-summary
        github-token: ${{ inputs.github_token }}
