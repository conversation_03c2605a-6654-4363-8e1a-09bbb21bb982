## Building and running

Before submitting any changes, it is crucial to validate them by running the full preflight check. This command will build the repository, run all tests, check for type errors, and lint the code.

To run the full suite of checks, execute the following command:

```bash
npm run preflight
```

This single command ensures that your changes meet all the quality gates of the project. While you can run the individual steps (`build`, `test`, `typecheck`, `lint`) separately, it is highly recommended to use `npm run preflight` to ensure a comprehensive validation.

## Writing Tests

This project uses **Vitest** as its primary testing framework. When writing tests, aim to follow existing patterns. Key conventions include:

### Test Structure and Framework

- **Framework**: All tests are written using Vitest (`describe`, `it`, `expect`, `vi`).
- **File Location**: Test files (`*.test.ts` for logic, `*.test.tsx` for React components) are co-located with the source files they test.
- **Configuration**: Test environments are defined in `vitest.config.ts` files.
- **Setup/Teardown**: Use `beforeEach` and `afterEach`. Commonly, `vi.resetAllMocks()` is called in `beforeEach` and `vi.restoreAllMocks()` in `afterEach`.

### Mocking (`vi` from Vitest)

- **ES Modules**: Mock with `vi.mock('module-name', async (importOriginal) => { ... })`. Use `importOriginal` for selective mocking.
  - _Example_: `vi.mock('os', async (importOriginal) => { const actual = await importOriginal(); return { ...actual, homedir: vi.fn() }; });`
- **Mocking Order**: For critical dependencies (e.g., `os`, `fs`) that affect module-level constants, place `vi.mock` at the _very top_ of the test file, before other imports.
- **Hoisting**: Use `const myMock = vi.hoisted(() => vi.fn());` if a mock function needs to be defined before its use in a `vi.mock` factory.
- **Mock Functions**: Create with `vi.fn()`. Define behavior with `mockImplementation()`, `mockResolvedValue()`, or `mockRejectedValue()`.
- **Spying**: Use `vi.spyOn(object, 'methodName')`. Restore spies with `mockRestore()` in `afterEach`.

### Commonly Mocked Modules

- **Node.js built-ins**: `fs`, `fs/promises`, `os` (especially `os.homedir()`), `path`, `child_process` (`execSync`, `spawn`).
- **External SDKs**: `@google/genai`, `@modelcontextprotocol/sdk`.
- **Internal Project Modules**: Dependencies from other project packages are often mocked.

### React Component Testing (CLI UI - Ink)

- Use `render()` from `ink-testing-library`.
- Assert output with `lastFrame()`.
- Wrap components in necessary `Context.Provider`s.
- Mock custom React hooks and complex child components using `vi.mock()`.

### Asynchronous Testing

- Use `async/await` for asynchronous operations.
- For promises, use `expect(promise).resolves.toBe(value)` or `expect(promise).rejects.toThrow()`.
- For testing timers, use `vi.useFakeTimers()` and `vi.advanceTimersByTime()`.

### Test Data and Fixtures

- Create test data inline or in separate fixture files.
- Use `vi.mocked()` to get proper TypeScript typing for mocked functions.
- For file system operations, prefer mocking over creating actual files.

## Code Style and Conventions

### General Guidelines

- **TypeScript**: All code should be written in TypeScript with strict type checking enabled.
- **ESLint**: Follow the project's ESLint configuration. Run `npm run lint` to check for violations.
- **Prettier**: Code formatting is handled by Prettier. Run `npm run format` to auto-format code.
- **Imports**: Use ES6 imports. Prefer named imports over default imports when possible.

### File Organization

- **Barrel Exports**: Use `index.ts` files to re-export public APIs from directories.
- **Co-location**: Keep related files (tests, types, utilities) close to the code they support.
- **Naming**: Use descriptive names. Files should be kebab-case, classes should be PascalCase, functions and variables should be camelCase.

### Error Handling

- **Errors**: Use proper Error objects, not strings.
- **Async Errors**: Always handle promise rejections.
- **User-Facing Errors**: Provide clear, actionable error messages.

## Architecture Guidelines

### Package Structure

- **`packages/cli`**: User interface and command-line interaction logic.
- **`packages/core`**: Business logic, API communication, and tool execution.
- **Clear Boundaries**: CLI should not contain business logic; Core should not contain UI logic.

### Dependencies

- **Internal Dependencies**: CLI depends on Core, but Core should not depend on CLI.
- **External Dependencies**: Minimize external dependencies. Prefer well-maintained, popular packages.
- **Version Pinning**: Pin dependency versions to ensure reproducible builds.

### Configuration

- **Environment Variables**: Use environment variables for configuration that varies between environments.
- **Settings Files**: Use JSON configuration files for user preferences and project settings.
- **Validation**: Always validate configuration inputs.

## Performance Considerations

### Memory Management

- **Large Files**: Stream large files instead of loading them entirely into memory.
- **Caching**: Implement appropriate caching for expensive operations.
- **Cleanup**: Properly dispose of resources (file handles, network connections).

### Async Operations

- **Concurrency**: Use appropriate concurrency patterns (Promise.all, async iterators).
- **Timeouts**: Implement timeouts for network operations and external tool calls.
- **Cancellation**: Support operation cancellation where appropriate.

## Security Guidelines

### Input Validation

- **User Input**: Always validate and sanitize user input.
- **File Paths**: Validate file paths to prevent directory traversal attacks.
- **Shell Commands**: Be extremely careful with shell command construction. Use proper escaping.

### Secrets Management

- **API Keys**: Never commit API keys or secrets to the repository.
- **Environment Variables**: Use environment variables for sensitive configuration.
- **Logging**: Ensure secrets are not logged or exposed in error messages.

## Documentation

### Code Documentation

- **JSDoc**: Use JSDoc comments for public APIs and complex functions.
- **README**: Keep README files up to date with current functionality.
- **Examples**: Provide clear examples in documentation.

### Comments

- **Why, Not What**: Comments should explain why something is done, not what is being done.
- **Complex Logic**: Add comments for complex algorithms or business logic.
- **TODOs**: Use TODO comments sparingly and include context.

## Git and Version Control

### Commit Messages

- **Format**: Use conventional commit format: `type(scope): description`.
- **Types**: feat, fix, docs, style, refactor, test, chore.
- **Description**: Keep the first line under 50 characters, be descriptive.

### Branching

- **Feature Branches**: Create feature branches for new work.
- **Pull Requests**: Use pull requests for code review.
- **Clean History**: Squash commits when appropriate to maintain a clean history.

## Debugging and Troubleshooting

### Logging

- **Debug Mode**: Use debug mode for verbose logging during development.
- **Log Levels**: Use appropriate log levels (error, warn, info, debug).
- **Structured Logging**: Use structured logging for better searchability.

### Development Tools

- **Debugger**: Use Node.js debugger for step-through debugging.
- **Source Maps**: Ensure source maps are available for debugging TypeScript.
- **Dev Tools**: Leverage browser dev tools for React component debugging.

## Release and Deployment

### Build Process

- **Clean Builds**: Always start with a clean build for releases.
- **Testing**: Run full test suite before releasing.
- **Version Bumping**: Follow semantic versioning principles.

### Packaging

- **Bundle Size**: Monitor and optimize bundle size.
- **Dependencies**: Ensure all dependencies are properly declared.
- **Platform Support**: Test on all supported platforms (Windows, macOS, Linux).

## Contributing Guidelines

### Before Contributing

- **Issues**: Check existing issues before creating new ones.
- **Discussion**: Discuss major changes before implementing them.
- **Documentation**: Update documentation for new features.

### Code Review

- **Self Review**: Review your own code before submitting.
- **Testing**: Ensure all tests pass and add tests for new functionality.
- **Documentation**: Update relevant documentation.

### Community

- **Be Respectful**: Follow the code of conduct in all interactions.
- **Help Others**: Help other contributors when possible.
- **Learn**: Be open to feedback and continuous learning.
